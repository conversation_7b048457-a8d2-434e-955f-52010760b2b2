import Image from "next/image";
import { Button } from "@/components/ui/8bit/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/ui/8bit/card";

export default function Home() {
  return (
    <div className="retro min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4 crt scanlines relative">
      <div className="container mx-auto max-w-4xl">
        <main className="flex flex-col gap-8 items-center text-center py-12">
          {/* Hero Section */}
          <Card className="w-full max-w-2xl bg-black/20 backdrop-blur-sm border-2 border-cyan-400/30">
            <CardHeader>
              <CardTitle className="text-2xl md:text-4xl text-white mb-4 glitch">
                🎮 Welcome to Mystique
              </CardTitle>
              <div className="flex justify-center mb-6">
                <Image
                  className="dark:invert pixelated hover:scale-110 transition-transform duration-300"
                  src="/next.svg"
                  alt="Next.js logo"
                  width={180}
                  height={38}
                  priority
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-white/90 space-y-4 text-sm">
                <p className="flex items-center justify-center gap-2">
                  ⚡ Get started by editing{" "}
                  <code className="bg-yellow-400/20 text-yellow-300 px-2 py-1 border border-yellow-400/30">
                    src/app/page.tsx
                  </code>
                </p>
                <p>💾 Save and see your changes instantly with hot reload!</p>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-4 items-center flex-col sm:flex-row">
            <Button
              asChild
              size="lg"
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <a
                href="https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  className="dark:invert"
                  src="/vercel.svg"
                  alt="Vercel logomark"
                  width={20}
                  height={20}
                />
                🚀 Deploy now
              </a>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white border-blue-400"
            >
              <a
                href="https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
                target="_blank"
                rel="noopener noreferrer"
              >
                📚 Read our docs
              </a>
            </Button>
          </div>
        </main>

        {/* Footer Links */}
        <footer className="flex gap-6 flex-wrap items-center justify-center py-8">
          <Button
            asChild
            variant="ghost"
            className="text-white/80 hover:text-white"
          >
            <a
              href="https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image
                aria-hidden
                src="/file.svg"
                alt="File icon"
                width={16}
                height={16}
                className="dark:invert"
              />
              🎓 Learn
            </a>
          </Button>
          <Button
            asChild
            variant="ghost"
            className="text-white/80 hover:text-white"
          >
            <a
              href="https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image
                aria-hidden
                src="/window.svg"
                alt="Window icon"
                width={16}
                height={16}
                className="dark:invert"
              />
              🎨 Examples
            </a>
          </Button>
          <Button
            asChild
            variant="ghost"
            className="text-white/80 hover:text-white"
          >
            <a
              href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image
                aria-hidden
                src="/globe.svg"
                alt="Globe icon"
                width={16}
                height={16}
                className="dark:invert"
              />
              🌐 Go to nextjs.org →
            </a>
          </Button>
        </footer>
      </div>
    </div>
  );
}
