"use client";

import { Turnstile } from "@marsidev/react-turnstile";
import { useState } from "react";
import { Button } from "@/components/ui/8bit/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/8bit/card";
import { Separator } from "@/components/ui/8bit/separator";
import EmailLogin from "./email-login";
import EmailSignup from "./email-signup";
import Github from "./github";
import Google from "./google";

export default () => {
  const [activeTab, setActiveTab] = useState<"login" | "signup">("login");
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);

  return (
    <div className="retro min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4 flex items-center justify-center">
      <Card className="w-full max-w-md bg-black/20 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-2xl text-white text-center mb-4">
            🎮 Welcome Back
          </CardTitle>

          {/* Tab Navigation */}
          <div className="flex gap-2 mb-4">
            <Button
              onClick={() => setActiveTab("login")}
              variant={activeTab === "login" ? "default" : "outline"}
              className={`flex-1 ${
                activeTab === "login"
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "bg-transparent border-blue-400 text-blue-300 hover:bg-blue-600/20"
              }`}
            >
              🔑 Sign In
            </Button>
            <Button
              onClick={() => setActiveTab("signup")}
              variant={activeTab === "signup" ? "default" : "outline"}
              className={`flex-1 ${
                activeTab === "signup"
                  ? "bg-green-600 hover:bg-green-700 text-white"
                  : "bg-transparent border-green-400 text-green-300 hover:bg-green-600/20"
              }`}
            >
              ✨ Sign Up
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Social Login */}
          <div>
            <h3 className="text-sm font-medium text-white/80 mb-3 text-center">
              🚀 Continue with:
            </h3>
            <div className="flex gap-3">
              <div className="flex-1">
                <Github />
              </div>
              <div className="flex-1">
                <Google />
              </div>
            </div>
          </div>

          {/* Separator */}
          <div className="relative">
            <Separator className="bg-white/20" />
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="px-3 bg-black/40 text-white/60 text-sm">
                ⚡ Or
              </span>
            </div>
          </div>

          {/* Captcha */}
          <div className="flex justify-center">
            <Turnstile
              siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY!}
              onSuccess={setCaptchaToken}
              onError={() => setCaptchaToken(null)}
              onExpire={() => setCaptchaToken(null)}
            />
          </div>

          {/* Email Forms */}
          <div>
            {activeTab === "login" ? (
              <EmailLogin captchaToken={captchaToken} />
            ) : (
              <EmailSignup captchaToken={captchaToken} />
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
